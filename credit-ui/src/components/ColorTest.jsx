import React from 'react';
import { getChartColors, getThemeColors, getCSSCustomProperties } from '../utils/colorUtils';

const ColorTest = () => {
  const chartColors = getChartColors();
  const themeColors = getThemeColors();
  const cssProps = getCSSCustomProperties();

  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-bold">Color System Test</h2>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Chart Colors (Hex)</h3>
        <div className="flex gap-4">
          {chartColors.map((color, index) => (
            <div key={index} className="text-center">
              <div 
                className="w-16 h-16 rounded border"
                style={{ backgroundColor: color }}
              ></div>
              <p className="text-xs mt-1">chart-{index + 1}</p>
              <p className="text-xs text-muted-foreground">{color}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Theme Colors</h3>
        <div className="flex gap-4">
          <div className="text-center">
            <div 
              className="w-16 h-16 rounded border"
              style={{ backgroundColor: themeColors.primary }}
            ></div>
            <p className="text-xs mt-1">Primary</p>
            <p className="text-xs text-muted-foreground">{themeColors.primary}</p>
          </div>
          <div className="text-center">
            <div 
              className="w-16 h-16 rounded border"
              style={{ backgroundColor: themeColors.secondary }}
            ></div>
            <p className="text-xs mt-1">Secondary</p>
            <p className="text-xs text-muted-foreground">{themeColors.secondary}</p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">CSS Custom Properties</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Foreground:</strong> {cssProps.foreground}
          </div>
          <div>
            <strong>Background:</strong> {cssProps.background}
          </div>
          <div>
            <strong>Muted Foreground:</strong> {cssProps.mutedForeground}
          </div>
          <div>
            <strong>Border:</strong> {cssProps.border}
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">CSS Variables Test</h3>
        <div className="flex gap-4">
          <div 
            className="w-16 h-16 rounded border"
            style={{ backgroundColor: 'hsl(var(--chart-1))' }}
          ></div>
          <div 
            className="w-16 h-16 rounded border"
            style={{ backgroundColor: 'hsl(var(--chart-2))' }}
          ></div>
          <div 
            className="w-16 h-16 rounded border"
            style={{ backgroundColor: 'hsl(var(--chart-3))' }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default ColorTest;
