import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, Cell, CartesianGrid } from 'recharts';
import { getChartColors, getCSSCustomProperties } from '../../utils/colorUtils';

export default function BarGraph({ data, grid, height = 400 }) {
    const chartColors = getChartColors();

    // Assign theme colors to data if not already provided
    const dataWithThemeColors = data.map((entry, index) => ({
        ...entry,
        color: entry.color || chartColors[index % chartColors.length]
    }));

    return (
        <ResponsiveContainer width="100%" height={height}>
            <BarChart data={dataWithThemeColors}>
                <XAxis
                    className='text-xs font-semibold'
                    dataKey="name"
                    tick={{ fill: 'var(--muted-foreground)' }}
                />
                <YAxis
                    className='text-xs font-semibold'
                    tick={{ fill: 'var(--muted-foreground)' }}
                />
                {grid && <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />}
                <Tooltip
                    contentStyle={{
                        backgroundColor: 'var(--popover)',
                        border: '1px solid var(--border)',
                        borderRadius: '6px',
                        color: 'var(--popover-foreground)'
                    }}
                />
                <Bar barSize={'10%'} dataKey="value">
                    {
                        dataWithThemeColors.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                        ))
                    }
                </Bar>
            </BarChart>
        </ResponsiveContainer>
    );
};