import React from 'react';
import { BiLoaderCircle } from 'react-icons/bi';

const CustomLoader = ({ 
  width = 400, 
  height = 400, 
  size = 'large',
  text = 'Loading...',
  variant = 'spinner' 
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'w-6 h-6';
      case 'medium':
        return 'w-12 h-12';
      case 'large':
        return 'w-16 h-16';
      case 'xl':
        return 'w-24 h-24';
      default:
        return 'w-12 h-12';
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'small':
        return 'text-sm';
      case 'medium':
        return 'text-base';
      case 'large':
        return 'text-lg';
      case 'xl':
        return 'text-xl';
      default:
        return 'text-base';
    }
  };

  if (variant === 'skeleton') {
    return (
      <div 
        className="flex flex-col items-center justify-center space-y-4 p-8"
        style={{ width, height }}
      >
        <div className="animate-pulse space-y-4 w-full max-w-md">
          <div className="h-4 bg-muted rounded w-3/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
          <div className="h-4 bg-muted rounded w-5/6"></div>
          <div className="h-8 bg-muted rounded w-full"></div>
        </div>
        {text && (
          <div className={`font-medium text-muted-foreground ${getTextSize()}`}>
            {text}
          </div>
        )}
      </div>
    );
  }

  if (variant === 'dots') {
    return (
      <div 
        className="flex flex-col items-center justify-center space-y-4"
        style={{ width, height }}
      >
        <div className="flex space-x-2">
          <div className="w-3 h-3 bg-primary rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-3 h-3 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        {text && (
          <div className={`font-medium text-foreground ${getTextSize()}`}>
            {text}
          </div>
        )}
      </div>
    );
  }

  if (variant === 'pulse') {
    return (
      <div 
        className="flex flex-col items-center justify-center space-y-4"
        style={{ width, height }}
      >
        <div className={`bg-primary rounded-full animate-pulse ${getSizeClasses()}`}></div>
        {text && (
          <div className={`font-medium text-foreground ${getTextSize()}`}>
            {text}
          </div>
        )}
      </div>
    );
  }

  // Default spinner variant
  return (
    <div 
      className="flex flex-col items-center justify-center space-y-4"
      style={{ width, height }}
    >
      <BiLoaderCircle className={`animate-spin text-primary ${getSizeClasses()}`} />
      {text && (
        <div className={`font-medium text-foreground ${getTextSize()}`}>
          {text}
        </div>
      )}
    </div>
  );
};

// Specialized loading components for different use cases
export const TableLoader = ({ rows = 5 }) => (
  <div className="space-y-3 p-4">
    {Array.from({ length: rows }).map((_, i) => (
      <div key={i} className="animate-pulse flex space-x-4">
        <div className="rounded-full bg-muted h-10 w-10"></div>
        <div className="flex-1 space-y-2 py-1">
          <div className="h-4 bg-muted rounded w-3/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </div>
      </div>
    ))}
  </div>
);

export const ChartLoader = ({ height = 300 }) => (
  <div 
    className="flex items-center justify-center bg-card rounded-lg border"
    style={{ height }}
  >
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-pulse space-y-3 w-64">
        <div className="h-4 bg-muted rounded w-1/2 mx-auto"></div>
        <div className="space-y-2">
          <div className="h-32 bg-muted rounded"></div>
          <div className="flex space-x-2">
            <div className="h-4 bg-muted rounded flex-1"></div>
            <div className="h-4 bg-muted rounded flex-1"></div>
            <div className="h-4 bg-muted rounded flex-1"></div>
          </div>
        </div>
      </div>
      <CustomLoader size="medium" text="Loading chart data..." variant="spinner" />
    </div>
  </div>
);

export const CardLoader = () => (
  <div className="animate-pulse p-6 bg-card rounded-lg border">
    <div className="space-y-4">
      <div className="h-6 bg-muted rounded w-1/3"></div>
      <div className="h-16 bg-muted rounded"></div>
      <div className="space-y-2">
        <div className="h-4 bg-muted rounded w-3/4"></div>
        <div className="h-4 bg-muted rounded w-1/2"></div>
      </div>
    </div>
  </div>
);

export const AnalysisLoader = () => (
  <div className="flex flex-col items-center justify-center min-h-[400px] space-y-6">
    <div className="relative">
      <div className="w-20 h-20 border-4 border-muted rounded-full"></div>
      <div className="w-20 h-20 border-4 border-primary border-t-transparent rounded-full animate-spin absolute top-0 left-0"></div>
    </div>
    <div className="text-center space-y-2">
      <div className="text-lg font-semibold text-foreground">Analyzing Data</div>
      <div className="text-sm text-muted-foreground">Processing financial metrics and generating insights...</div>
    </div>
    <div className="flex space-x-1">
      <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
      <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
      <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
    </div>
  </div>
);

export default CustomLoader;
