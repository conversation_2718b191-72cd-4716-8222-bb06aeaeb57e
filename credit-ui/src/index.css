@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 0.625rem;
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --chart-1: 221.2 83.2% 53.3%;
  --chart-2: 173 58% 39%;
  --chart-3: 43 74% 66%;
  --chart-4: 27 87% 67%;
  --chart-5: 12 76% 61%;

  --sidebar: var(--background);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

.transflow-light {
  --background: 240 100% 98%; /* Very light blue-tinted background */
  --foreground: 240 100% 15%; /* Dark blue text */
  --card: 0 0% 100%; /* Pure white cards for contrast */
  --card-foreground: 240 100% 15%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 100% 15%;
  --primary: 210 100% 28%; /* #08518A - main transflow blue */
  --primary-foreground: 240 100% 98%;
  --secondary: 240 50% 92%; /* Light blue secondary */
  --secondary-foreground: 210 100% 28%;
  --muted: 240 50% 95%; /* Very light blue muted */
  --muted-foreground: 240 50% 40%; /* Darker blue for better contrast */
  --accent: 210 100% 28%; /* Use primary color for sidebar */
  --accent-foreground: 240 100% 98%; /* Light text on blue sidebar */
  --destructive: 0 84.2% 60.2%;
  --border: 240 50% 88%; /* Light blue borders */
  --input: 240 50% 95%; /* Light blue inputs */
  --ring: 210 100% 28%;
  --chart-1: 210 100% 28%; /* Primary blue for main chart */
  --chart-2: 180 50% 55%; /* Complementary teal */
  --chart-3: 300 50% 65%; /* Purple accent */
  --chart-4: 60 70% 45%; /* Orange accent */
  --chart-5: 120 50% 60%; /* Green accent */

  /* Sidebar specific colors */
  --sidebar: var(--accent); /* Blue sidebar */
  --sidebar-foreground: var(--accent-foreground); /* Light text on blue */
  --sidebar-primary: 210 100% 20%; /* Darker blue for primary elements */
  --sidebar-primary-foreground: 240 100% 98%;
  --sidebar-accent: 240 50% 40%; /* Slightly lighter blue for hover states */
  --sidebar-accent-foreground: 240 100% 98%;
  --sidebar-border: 240 50% 30%; /* Darker blue borders in sidebar */
  --sidebar-ring: 210 100% 20%;
}

.transflow-dark {
  --background: 240 100% 8%; /* Very dark blue background */
  --foreground: 240 50% 92%; /* Light blue-tinted text */
  --card: 240 50% 12%; /* Dark blue cards */
  --card-foreground: 240 50% 92%;
  --popover: 240 50% 15%; /* Slightly lighter dark blue for popovers */
  --popover-foreground: 240 50% 92%;
  --primary: 240 70% 65%; /* Bright blue for visibility in dark mode */
  --primary-foreground: 240 100% 8%; /* Dark text on bright blue */
  --secondary: 240 50% 20%; /* Dark blue secondary */
  --secondary-foreground: 240 50% 85%;
  --muted: 240 50% 18%; /* Dark blue muted */
  --muted-foreground: 240 50% 60%; /* Medium blue for muted text */
  --accent: 240 50% 25%; /* Dark blue for sidebar */
  --accent-foreground: 240 50% 85%; /* Light text on dark blue sidebar */
  --destructive: 15 70% 65%; /* Bright red for visibility */
  --border: 240 50% 25%; /* Dark blue borders */
  --input: 240 50% 15%; /* Dark blue inputs */
  --ring: 240 70% 65%;
  --chart-1: 240 70% 65%; /* Bright blue for main chart - good contrast */
  --chart-2: 180 60% 70%; /* Bright teal */
  --chart-3: 300 70% 75%; /* Bright purple */
  --chart-4: 60 80% 70%; /* Bright orange */
  --chart-5: 120 60% 70%; /* Bright green */

  /* Sidebar specific colors */
  --sidebar: var(--accent); /* Dark blue sidebar */
  --sidebar-foreground: var(--accent-foreground); /* Light text on dark blue */
  --sidebar-primary: 240 70% 65%; /* Bright blue for primary elements */
  --sidebar-primary-foreground: 240 100% 8%;
  --sidebar-accent: 240 60% 35%; /* Medium blue for hover states */
  --sidebar-accent-foreground: 240 50% 92%;
  --sidebar-border: 240 50% 20%; /* Subtle borders in sidebar */
  --sidebar-ring: 240 70% 65%;
}

@theme inline {
  --font-inter: 'Inter', 'sans-serif';
  --font-manrope: 'Manrope', 'sans-serif';
}

@layer base {
  * {
    border-color: hsl(var(--border));
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }
  html {
    overflow-x: hidden;
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    min-height: 100vh;
    width: 100%;
    font-family: var(--font-inter);
  }

  button:not(:disabled),
  [role='button']:not(:disabled) {
    cursor: pointer;
  }

  /* Prevent focus zoom on mobile devices */
  @media screen and (max-width: 767px) {
    input,
    select,
    textarea {
      font-size: 16px !important;
    }
  }
}

/* Custom utilities */
.container {
  margin-inline: auto;
  padding-inline: 2rem;
}

.no-scrollbar {
  /* Hide scrollbar for Chrome, Safari and Opera */
  -webkit-scrollbar: none;
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* styles.css */
.CollapsibleContent {
  overflow: hidden;
}
.CollapsibleContent[data-state='open'] {
  animation: slideDown 300ms ease-out;
}
.CollapsibleContent[data-state='closed'] {
  animation: slideUp 300ms ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}